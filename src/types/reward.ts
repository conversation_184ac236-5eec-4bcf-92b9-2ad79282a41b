export type GiftGivingFormValue = {
  account: string;
  reward: {
    type: CollectionMethod;
    rewardId: number;
    amount: number;
  }[];
  contentMapping: {
    [key: string]: { title: string };
  };
  templateId?: number;
  method: SendingMethod;
  frozenTime: number;
  expiredType: TimeLimitInputMethod;
  expiredTime: number | string;
  sendMail: number;
  mailContentMapping?: {
    [key: string]: { title: string; content: string };
  };
  note: {
    [key: string]: string;
  };
  frontenNote?: {
    [key: string]: string;
  };
};

export type ConfirmBatchCSVFormValue = {
  templateId?: number;
  method: number;
  expiredType: number;
  expiredTime: number | string;
  sendMail: number;
  mailContentMapping?: {
    [key: string]: {
      title: string;
      content: string;
    };
  };
  note: {
    [key: string]: string;
  };
  frontenNote?: {
    [key: string]: string;
  };
  rows: {
    activityName: string;
    playerAccount: string;
    rewardType: string;
    rewardItemId: string;
    rewardValue: number;
    frozenTime: number;
  }[];
};

export enum SendingMethod {
  SINGLE_SENDING = 2,
  BATCH_SENDING = 1
}

export enum RewardType {
  MAIN_POINTS = 1,
  PROP = 2
}

export enum CollectionMethod {
  REWARD_CENTER = 1,
  INSTANT_ARRIVAL = 2
}

export enum TimeLimitInputMethod {
  ENTER_HOUR = 1,
  ENTER_SPECIFIC_TIME = 2
}

export enum SendSiteMessageNotificationType {
  USE_EXISTING_TEMPLATE = 'use_existing_template',
  CUSTOMIZE_CONTENT = 'customize_content'
}

export type ContentMapping = {
  [key: string]: { title?: string; content?: string; photo?: string };
};

export type GiftRewardProp = {
  id: number;
  contentMapping: ContentMapping;
};

export type GiftMailTemplate = {
  id: number;
  contentMapping: ContentMapping;
};

export type GiftMailVariables = {
  id: number;
  label: string;
  variables: { key: string; label: string }[];
};

export type BatchCSVFileSummary = {
  total: number;
  valid: number;
  invalid: number;
};

export type BatchCSVFilePreview = {
  activityName: string;
  playerAccount: string;
  rewardType: string;
  rewardItemId: string;
  rewardValue: string;
  frozenTime: number;
  isValid: boolean;
  errors: string[];
};

export type PreviewBatchCSVFile = {
  summary: BatchCSVFileSummary;
  preview: BatchCSVFilePreview[];
};
